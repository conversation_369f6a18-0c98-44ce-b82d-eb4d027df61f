/**
 * 🚫 LocalStorage Disabler - Memory Issue Fix
 * 
 * This utility completely disables localStorage fallbacks to prevent memory issues
 * caused by excessive localStorage usage. The app will now rely solely on Supabase
 * for data persistence.
 */

// Override localStorage methods to prevent any usage
const disableLocalStorage = () => {
    // Create a no-op localStorage replacement
    const noOpStorage = {
        getItem: (key) => {
            console.warn(`🚫 localStorage.getItem('${key}') blocked - using Supabase only`);
            return null;
        },
        setItem: (key, value) => {
            console.warn(`🚫 localStorage.setItem('${key}') blocked - using Supabase only`);
            // Don't actually store anything
        },
        removeItem: (key) => {
            console.warn(`🚫 localStorage.removeItem('${key}') blocked - using Supabase only`);
            // Don't actually remove anything
        },
        clear: () => {
            console.warn('🚫 localStorage.clear() blocked - using Supabase only');
            // Don't actually clear anything
        },
        key: (index) => {
            console.warn(`🚫 localStorage.key(${index}) blocked - using Supabase only`);
            return null;
        },
        get length() {
            return 0;
        }
    };

    // Only block non-essential localStorage usage
    // Keep auth tokens working by creating a selective blocker
    const originalSetItem = localStorage.setItem;
    const originalGetItem = localStorage.getItem;
    const originalRemoveItem = localStorage.removeItem;

    // List of allowed keys (auth tokens only)
    const allowedKeys = [
        'sb-csibhnfqpwqkhpnvdakz-auth-token',
        'sb-csibhnfqpwqkhpnvdakz-auth-token-code-verifier',
        'supabase.auth.token',
        'remember_me_auth'
    ];

    // Override setItem to block non-auth data
    localStorage.setItem = function(key, value) {
        if (allowedKeys.some(allowedKey => key.includes(allowedKey))) {
            // Allow auth tokens
            return originalSetItem.call(this, key, value);
        } else {
            // Block everything else
            console.warn(`🚫 localStorage.setItem('${key}') blocked - non-auth data not allowed`);
            return;
        }
    };

    // Override getItem to block non-auth data
    localStorage.getItem = function(key) {
        if (allowedKeys.some(allowedKey => key.includes(allowedKey))) {
            // Allow auth tokens
            return originalGetItem.call(this, key);
        } else {
            // Block everything else
            console.warn(`🚫 localStorage.getItem('${key}') blocked - non-auth data not allowed`);
            return null;
        }
    };

    // Override removeItem to block non-auth data
    localStorage.removeItem = function(key) {
        if (allowedKeys.some(allowedKey => key.includes(allowedKey))) {
            // Allow auth tokens
            return originalRemoveItem.call(this, key);
        } else {
            // Block everything else
            console.warn(`🚫 localStorage.removeItem('${key}') blocked - non-auth data not allowed`);
            return;
        }
    };

    console.log('✅ LocalStorage usage restricted to auth tokens only');
};

// Clean up existing non-auth localStorage data
const cleanupExistingData = () => {
    try {
        const keysToRemove = [];
        
        // Get all keys
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) {
                // Check if it's not an auth token
                const isAuthToken = [
                    'sb-csibhnfqpwqkhpnvdakz-auth-token',
                    'sb-csibhnfqpwqkhpnvdakz-auth-token-code-verifier',
                    'supabase.auth.token',
                    'remember_me_auth'
                ].some(allowedKey => key.includes(allowedKey));

                if (!isAuthToken) {
                    keysToRemove.push(key);
                }
            }
        }

        // Remove non-auth data
        keysToRemove.forEach(key => {
            try {
                localStorage.removeItem(key);
                console.log(`🧹 Removed non-auth data: ${key}`);
            } catch (error) {
                console.warn(`Failed to remove ${key}:`, error);
            }
        });

        console.log(`✅ Cleaned up ${keysToRemove.length} non-auth localStorage items`);
        
        return {
            success: true,
            removedCount: keysToRemove.length,
            removedKeys: keysToRemove
        };
    } catch (error) {
        console.error('Failed to cleanup localStorage:', error);
        return {
            success: false,
            error: error.message
        };
    }
};

// Initialize the localStorage disabler
export const initializeLocalStorageDisabler = () => {
    console.log('🚫 Initializing localStorage disabler...');
    
    // First clean up existing data
    const cleanupResult = cleanupExistingData();
    
    // Then disable future localStorage usage
    disableLocalStorage();
    
    // Make cleanup function available globally for debugging
    window.cleanupNonAuthStorage = cleanupExistingData;
    
    console.log('✅ LocalStorage disabler initialized');
    
    return cleanupResult;
};

// Export cleanup function for manual use
export { cleanupExistingData };
