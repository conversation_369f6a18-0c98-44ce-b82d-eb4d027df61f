/**
 * 🧪 Memory Test Utilities
 * 
 * Utilities to test and verify that localStorage memory issues are resolved
 */

// Test localStorage blocking
export const testLocalStorageBlocking = () => {
    console.log('🧪 Testing localStorage blocking...');
    
    const testResults = {
        authTokensAllowed: false,
        nonAuthDataBlocked: false,
        errors: []
    };

    try {
        // Test 1: Auth tokens should be allowed
        console.log('Testing auth token storage...');
        localStorage.setItem('sb-csibhnfqpwqkhpnvdakz-auth-token', 'test-auth-token');
        const authToken = localStorage.getItem('sb-csibhnfqpwqkhpnvdakz-auth-token');
        if (authToken === 'test-auth-token') {
            testResults.authTokensAllowed = true;
            console.log('✅ Auth tokens are allowed');
            localStorage.removeItem('sb-csibhnfqpwqkhpnvdakz-auth-token');
        } else {
            console.log('❌ Auth tokens are blocked (unexpected)');
        }
    } catch (error) {
        testResults.errors.push(`Auth token test failed: ${error.message}`);
        console.log('❌ Auth token test failed:', error.message);
    }

    try {
        // Test 2: Non-auth data should be blocked
        console.log('Testing non-auth data blocking...');
        localStorage.setItem('user_generation_history', JSON.stringify([{test: 'data'}]));
        const nonAuthData = localStorage.getItem('user_generation_history');
        if (nonAuthData === null) {
            testResults.nonAuthDataBlocked = true;
            console.log('✅ Non-auth data is blocked');
        } else {
            console.log('❌ Non-auth data is not blocked (unexpected)');
        }
    } catch (error) {
        testResults.errors.push(`Non-auth data test failed: ${error.message}`);
        console.log('❌ Non-auth data test failed:', error.message);
    }

    // Test 3: Try to store large data (should be blocked)
    try {
        console.log('Testing large data blocking...');
        const largeData = 'x'.repeat(1024 * 1024); // 1MB of data
        localStorage.setItem('large_test_data', largeData);
        const retrievedLargeData = localStorage.getItem('large_test_data');
        if (retrievedLargeData === null) {
            console.log('✅ Large data storage is blocked');
        } else {
            console.log('❌ Large data storage is not blocked');
        }
    } catch (error) {
        console.log('✅ Large data storage failed as expected:', error.message);
    }

    const overallSuccess = testResults.authTokensAllowed && testResults.nonAuthDataBlocked;
    
    console.log('\n📊 Test Results:');
    console.log(`- Auth tokens allowed: ${testResults.authTokensAllowed ? '✅' : '❌'}`);
    console.log(`- Non-auth data blocked: ${testResults.nonAuthDataBlocked ? '✅' : '❌'}`);
    console.log(`- Overall success: ${overallSuccess ? '✅' : '❌'}`);
    
    if (testResults.errors.length > 0) {
        console.log('- Errors:', testResults.errors);
    }

    return {
        success: overallSuccess,
        details: testResults
    };
};

// Check current localStorage usage
export const checkCurrentStorageUsage = () => {
    console.log('📊 Current localStorage usage:');
    
    let totalSize = 0;
    let itemCount = 0;
    const items = [];

    try {
        for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            if (key) {
                const value = localStorage.getItem(key);
                if (value) {
                    const size = value.length;
                    totalSize += size;
                    itemCount++;
                    items.push({
                        key,
                        size,
                        sizeFormatted: formatBytes(size),
                        isAuthToken: isAuthToken(key)
                    });
                }
            }
        }

        items.sort((a, b) => b.size - a.size);

        console.log(`Total items: ${itemCount}`);
        console.log(`Total size: ${formatBytes(totalSize)}`);
        console.log('\nItems by size:');
        items.forEach(item => {
            const type = item.isAuthToken ? '[AUTH]' : '[DATA]';
            console.log(`  ${type} ${item.key}: ${item.sizeFormatted}`);
        });

        return {
            totalItems: itemCount,
            totalSize,
            totalSizeFormatted: formatBytes(totalSize),
            items
        };
    } catch (error) {
        console.error('Failed to check storage usage:', error);
        return {
            error: error.message
        };
    }
};

// Helper function to check if a key is an auth token
const isAuthToken = (key) => {
    const authKeys = [
        'sb-csibhnfqpwqkhpnvdakz-auth-token',
        'sb-csibhnfqpwqkhpnvdakz-auth-token-code-verifier',
        'supabase.auth.token',
        'remember_me_auth'
    ];
    return authKeys.some(authKey => key.includes(authKey));
};

// Helper function to format bytes
const formatBytes = (bytes) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// Make functions available globally for console testing
if (typeof window !== 'undefined') {
    window.testLocalStorageBlocking = testLocalStorageBlocking;
    window.checkCurrentStorageUsage = checkCurrentStorageUsage;
}
